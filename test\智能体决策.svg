<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2434.406005859375 2075.625" style="max-width: 2434.406005859375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072"><style>#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .error-icon{fill:#a44141;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edge-thickness-normal{stroke-width:1px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .marker.cross{stroke:lightgrey;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 p{margin:0;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .cluster-label text{fill:#F9FFFE;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .cluster-label span{color:#F9FFFE;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .cluster-label span p{background-color:transparent;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .label text,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 span{fill:#ccc;color:#ccc;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node rect,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node circle,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node ellipse,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node polygon,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .rough-node .label text,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node .label text,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .image-shape .label,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .icon-shape .label{text-anchor:middle;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .rough-node .label,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node .label,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .image-shape .label,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .icon-shape .label{text-align:center;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .node.clickable{cursor:pointer;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .arrowheadPath{fill:lightgrey;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .cluster text{fill:#F9FFFE;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .cluster span{color:#F9FFFE;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 rect.text{fill:none;stroke-width:0;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .icon-shape,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .icon-shape p,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .icon-shape rect,#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="外部AI服务调用" class="cluster"><rect height="104" width="1184.71875" y="1809.625" x="503.5" style=""></rect><g transform="translate(1040.9093742370605, 1809.625)" class="cluster-label"><foreignObject height="24" width="109.9000015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>外部AI服务调用</p></span></div></foreignObject></g></g><g data-look="classic" id="AI决策引擎" class="cluster"><rect height="104" width="2026.21875" y="1963.625" x="199" style=""></rect><g transform="translate(1173.1593742370605, 1963.625)" class="cluster-label"><foreignObject height="24" width="77.9000015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI决策引擎</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1475.188,62L1475.188,66.167C1475.188,70.333,1475.188,78.667,1475.258,86.417C1475.328,94.167,1475.469,101.334,1475.539,104.917L1475.609,108.501"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M1412.074,198.887L1318.309,215.572C1224.544,232.258,1037.014,265.629,943.249,287.814C849.484,310,849.484,321,849.484,326.5L849.484,332"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_2" d="M1541.947,196.241L1672.326,213.367C1802.704,230.494,2063.461,264.747,2193.84,287.373C2324.219,310,2324.219,321,2324.219,326.5L2324.219,332"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C1_3" d="M849.484,390L849.484,394.167C849.484,398.333,849.484,406.667,849.484,414.333C849.484,422,849.484,429,849.484,432.5L849.484,436"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C1_C2_4" d="M849.484,494L849.484,498.167C849.484,502.333,849.484,510.667,849.484,518.333C849.484,526,849.484,533,849.484,536.5L849.484,540"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C2_C3_5" d="M787.484,587.216L764.682,593.18C741.88,599.144,696.276,611.072,673.544,620.62C650.812,630.167,650.953,637.334,651.023,640.917L651.093,644.501"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C3_C4_6" d="M626.995,764.198L621.524,774.311C616.054,784.424,605.113,804.649,599.642,827.418C594.172,850.188,594.172,875.5,594.172,888.156L594.172,900.813"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C3_C5_7" d="M707.914,731.633L775.017,747.173C842.12,762.713,976.326,793.794,1043.428,827.158C1110.531,860.521,1110.531,896.167,1110.531,931.813C1110.531,967.458,1110.531,1003.104,1110.531,1031.594C1110.531,1060.083,1110.531,1081.417,1110.531,1100.75C1110.531,1120.083,1110.531,1137.417,1110.531,1160.573C1110.531,1183.729,1110.531,1212.708,1110.531,1243.688C1110.531,1274.667,1110.531,1307.646,1110.531,1337.635C1110.531,1367.625,1110.531,1394.625,1110.531,1408.125L1110.531,1421.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C4_C6_8" d="M542.168,958.813L516.507,972.135C490.846,985.458,439.525,1012.104,413.864,1030.927C388.203,1049.75,388.203,1060.75,388.203,1066.25L388.203,1071.75"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C6_C7_9" d="M334.706,1129.75L326.45,1133.917C318.195,1138.083,301.683,1146.417,293.498,1154.167C285.312,1161.917,285.453,1169.084,285.523,1172.667L285.593,1176.251"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C7_C8_10" d="M254.693,1273.146L243.273,1284.393C231.853,1295.639,209.012,1318.132,197.592,1342.879C186.172,1367.625,186.172,1394.625,186.172,1408.125L186.172,1421.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C7_C9_11" d="M316.65,1273.146L327.904,1284.393C339.158,1295.639,361.665,1318.132,372.918,1342.879C384.172,1367.625,384.172,1394.625,384.172,1408.125L384.172,1421.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C8_C10_12" d="M186.172,1479.625L186.172,1493.792C186.172,1507.958,186.172,1536.292,205.538,1556.718C224.903,1577.144,263.634,1589.663,283,1595.923L302.366,1602.183"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C9_C10_13" d="M384.172,1479.625L384.172,1493.792C384.172,1507.958,384.172,1536.292,384.172,1555.958C384.172,1575.625,384.172,1586.625,384.172,1592.125L384.172,1597.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C5_C10_14" d="M1110.531,1479.625L1110.531,1493.792C1110.531,1507.958,1110.531,1536.292,1003.135,1559.921C895.74,1583.55,680.948,1602.476,573.552,1611.939L466.156,1621.401"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C10_C11_15" d="M333.076,1655.625L325.191,1659.792C317.306,1663.958,301.536,1672.292,293.651,1679.958C285.766,1687.625,285.766,1694.625,285.766,1698.125L285.766,1701.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C11_C12_16" d="M207.766,1752.929L187.471,1758.211C167.177,1763.494,126.589,1774.06,106.294,1783.509C86,1792.958,86,1801.292,86,1808.958C86,1816.625,86,1823.625,86,1827.125L86,1830.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C12_C13_17" d="M86,1888.625L86,1892.792C86,1896.958,86,1905.292,86,1913.625C86,1921.958,86,1930.292,86,1938.625C86,1946.958,86,1955.292,86,1962.958C86,1970.625,86,1977.625,86,1981.125L86,1984.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_D1_18" d="M2324.219,390L2324.219,394.167C2324.219,398.333,2324.219,406.667,2324.219,414.333C2324.219,422,2324.219,429,2324.219,432.5L2324.219,436"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_D2_19" d="M2324.219,494L2324.219,498.167C2324.219,502.333,2324.219,510.667,2324.219,518.333C2324.219,526,2324.219,533,2324.219,536.5L2324.219,540"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_D3_20" d="M2324.219,598L2324.219,602.167C2324.219,606.333,2324.219,614.667,2324.219,629.49C2324.219,644.313,2324.219,665.625,2324.219,676.281L2324.219,686.938"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_D4_21" d="M2259.532,744.938L2227.612,758.26C2195.693,771.583,2131.854,798.229,2100.009,817.135C2068.165,836.042,2068.313,847.209,2068.388,852.792L2068.462,858.375"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4_D5_22" d="M2025.75,959.484L2004.742,972.695C1983.734,985.906,1941.719,1012.328,1920.711,1031.039C1899.703,1049.75,1899.703,1060.75,1899.703,1066.25L1899.703,1071.75"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4_D6_23" d="M2117.226,953.54L2149.923,967.741C2182.619,981.943,2248.013,1010.347,2280.71,1030.048C2313.406,1049.75,2313.406,1060.75,2313.406,1066.25L2313.406,1071.75"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D5_D7_24" d="M1843.107,1129.75L1834.373,1133.917C1825.639,1138.083,1808.171,1146.417,1799.437,1159.906C1790.703,1173.396,1790.703,1192.042,1790.703,1201.365L1790.703,1210.688"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D6_D7_25" d="M2313.406,1129.75L2313.406,1133.917C2313.406,1138.083,2313.406,1146.417,2239.947,1162.801C2166.487,1179.186,2019.568,1203.622,1946.108,1215.84L1872.649,1228.058"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D7_D8_26" d="M1790.703,1268.688L1790.703,1280.677C1790.703,1292.667,1790.703,1316.646,1790.778,1334.219C1790.852,1351.792,1791.001,1362.959,1791.075,1368.542L1791.15,1374.125"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D8_D9_27" d="M1747.605,1484.527L1728.871,1497.877C1710.138,1511.226,1672.67,1537.926,1653.937,1556.775C1635.203,1575.625,1635.203,1586.625,1635.203,1592.125L1635.203,1597.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D8_D10_28" d="M1811.834,1507.494L1815.396,1517.016C1818.957,1526.538,1826.08,1545.581,1829.642,1560.603C1833.203,1575.625,1833.203,1586.625,1833.203,1592.125L1833.203,1597.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D9_D11_29" d="M1610.02,1655.625L1606.134,1659.792C1602.248,1663.958,1594.476,1672.292,1590.589,1679.958C1586.703,1687.625,1586.703,1694.625,1586.703,1698.125L1586.703,1701.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D10_D11_30" d="M1833.203,1655.625L1833.203,1659.792C1833.203,1663.958,1833.203,1672.292,1803.105,1682.808C1773.008,1693.323,1712.812,1706.022,1682.715,1712.371L1652.617,1718.72"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D11_D12_31" d="M1586.703,1759.625L1586.703,1763.792C1586.703,1767.958,1586.703,1776.292,1649.904,1784.625C1713.104,1792.958,1839.505,1801.292,1902.706,1808.958C1965.906,1816.625,1965.906,1823.625,1965.906,1827.125L1965.906,1830.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D12_D13_32" d="M1941.762,1888.625L1938.036,1892.792C1934.31,1896.958,1926.858,1905.292,1923.132,1913.625C1919.406,1921.958,1919.406,1930.292,1989.208,1938.625C2059.01,1946.958,2198.615,1955.292,2268.417,1962.958C2338.219,1970.625,2338.219,1977.625,2338.219,1981.125L2338.219,1984.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C2_E_33" d="M911.484,579.768L962.438,586.973C1013.391,594.178,1115.297,608.589,1166.25,631.618C1217.203,654.646,1217.203,686.292,1217.203,719.938C1217.203,753.583,1217.203,789.229,1217.203,824.875C1217.203,860.521,1217.203,896.167,1217.203,931.813C1217.203,967.458,1217.203,1003.104,1217.203,1031.594C1217.203,1060.083,1217.203,1081.417,1217.203,1100.75C1217.203,1120.083,1217.203,1137.417,1217.203,1160.573C1217.203,1183.729,1217.203,1212.708,1217.203,1243.688C1217.203,1274.667,1217.203,1307.646,1217.203,1342.802C1217.203,1377.958,1217.203,1415.292,1217.203,1452.625C1217.203,1489.958,1217.203,1527.292,1217.203,1556.625C1217.203,1585.958,1217.203,1607.292,1217.203,1626.625C1217.203,1645.958,1217.203,1663.292,1217.203,1680.625C1217.203,1697.958,1217.203,1715.292,1217.203,1732.625C1217.203,1749.958,1217.203,1767.292,1299.039,1780.125C1380.875,1792.958,1544.547,1801.292,1626.383,1814.125C1708.219,1826.958,1708.219,1844.292,1708.219,1861.625C1708.219,1878.958,1708.219,1896.292,1708.219,1909.125C1708.219,1921.958,1708.219,1930.292,1708.219,1938.625C1708.219,1946.958,1708.219,1955.292,1687.062,1964.937C1665.905,1974.582,1623.592,1985.539,1602.435,1991.017L1581.279,1996.496"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C6_F_34" d="M450.203,1122.717L466.781,1128.056C483.359,1133.394,516.516,1144.072,533.094,1163.901C549.672,1183.729,549.672,1212.708,549.672,1243.688C549.672,1274.667,549.672,1307.646,549.672,1342.802C549.672,1377.958,549.672,1415.292,549.672,1452.625C549.672,1489.958,549.672,1527.292,549.672,1556.625C549.672,1585.958,549.672,1607.292,549.672,1626.625C549.672,1645.958,549.672,1663.292,549.672,1680.625C549.672,1697.958,549.672,1715.292,549.672,1732.625C549.672,1749.958,549.672,1767.292,538.643,1780.125C527.615,1792.958,505.557,1801.292,494.529,1814.125C483.5,1826.958,483.5,1844.292,483.5,1861.625C483.5,1878.958,483.5,1896.292,483.5,1909.125C483.5,1921.958,483.5,1930.292,483.5,1938.625C483.5,1946.958,483.5,1955.292,476.158,1963.315C468.815,1971.338,454.13,1979.052,446.788,1982.908L439.445,1986.765"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C11_E_35" d="M314.185,1759.625L318.571,1763.792C322.957,1767.958,331.728,1776.292,336.114,1784.625C340.5,1792.958,340.5,1801.292,340.5,1814.125C340.5,1826.958,340.5,1844.292,340.5,1861.625C340.5,1878.958,340.5,1896.292,340.5,1909.125C340.5,1921.958,340.5,1930.292,340.5,1938.625C340.5,1946.958,340.5,1955.292,522.652,1967.575C704.803,1979.859,1069.107,1996.093,1251.259,2004.21L1433.41,2012.328"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_G_36" d="M2350.019,744.938L2362.751,758.26C2375.482,771.583,2400.944,798.229,2413.675,829.375C2426.406,860.521,2426.406,896.167,2426.406,931.813C2426.406,967.458,2426.406,1003.104,2426.406,1031.594C2426.406,1060.083,2426.406,1081.417,2426.406,1100.75C2426.406,1120.083,2426.406,1137.417,2426.406,1160.573C2426.406,1183.729,2426.406,1212.708,2426.406,1243.688C2426.406,1274.667,2426.406,1307.646,2426.406,1342.802C2426.406,1377.958,2426.406,1415.292,2426.406,1452.625C2426.406,1489.958,2426.406,1527.292,2426.406,1556.625C2426.406,1585.958,2426.406,1607.292,2426.406,1626.625C2426.406,1645.958,2426.406,1663.292,2426.406,1680.625C2426.406,1697.958,2426.406,1715.292,2426.406,1732.625C2426.406,1749.958,2426.406,1767.292,2426.406,1780.125C2426.406,1792.958,2426.406,1801.292,2426.406,1814.125C2426.406,1826.958,2426.406,1844.292,2426.406,1861.625C2426.406,1878.958,2426.406,1896.292,2426.406,1909.125C2426.406,1921.958,2426.406,1930.292,2374.042,1938.625C2321.677,1946.958,2216.948,1955.292,2164.583,1962.958C2112.219,1970.625,2112.219,1977.625,2112.219,1981.125L2112.219,1984.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D8_E_37" d="M1834.801,1484.527L1853.368,1497.877C1871.935,1511.226,1909.069,1537.926,1927.636,1561.942C1946.203,1585.958,1946.203,1607.292,1946.203,1626.625C1946.203,1645.958,1946.203,1663.292,1946.203,1680.625C1946.203,1697.958,1946.203,1715.292,1946.203,1732.625C1946.203,1749.958,1946.203,1767.292,1987.904,1780.125C2029.604,1792.958,2113.005,1801.292,2154.706,1814.125C2196.406,1826.958,2196.406,1844.292,2196.406,1861.625C2196.406,1878.958,2196.406,1896.292,2196.406,1909.125C2196.406,1921.958,2196.406,1930.292,2149.708,1938.625C2103.01,1946.958,2009.615,1955.292,1907.109,1966.557C1804.604,1977.822,1692.989,1992.019,1637.182,1999.118L1581.374,2006.216"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D12_H_38" d="M1971.099,1888.625L1971.9,1892.792C1972.701,1896.958,1974.304,1905.292,1975.105,1913.625C1975.906,1921.958,1975.906,1930.292,1942.375,1938.625C1908.844,1946.958,1841.781,1955.292,1821.871,1964.26C1801.961,1973.229,1829.204,1982.833,1842.825,1987.635L1856.446,1992.437"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C4_I_39" d="M672.172,952.089L727.732,966.533C783.292,980.976,894.411,1009.863,949.971,1034.973C1005.531,1060.083,1005.531,1081.417,1005.531,1100.75C1005.531,1120.083,1005.531,1137.417,1005.531,1160.573C1005.531,1183.729,1005.531,1212.708,1005.531,1243.688C1005.531,1274.667,1005.531,1307.646,1005.531,1342.802C1005.531,1377.958,1005.531,1415.292,1005.531,1452.625C1005.531,1489.958,1005.531,1527.292,1005.531,1556.625C1005.531,1585.958,1005.531,1607.292,1005.531,1626.625C1005.531,1645.958,1005.531,1663.292,1005.531,1680.625C1005.531,1697.958,1005.531,1715.292,1005.531,1732.625C1005.531,1749.958,1005.531,1767.292,1005.531,1780.125C1005.531,1792.958,1005.531,1801.292,1005.531,1808.958C1005.531,1816.625,1005.531,1823.625,1005.531,1827.125L1005.531,1830.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C10_J_40" d="M435.576,1655.625L443.508,1659.792C451.441,1663.958,467.306,1672.292,475.239,1685.125C483.172,1697.958,483.172,1715.292,483.172,1732.625C483.172,1749.958,483.172,1767.292,537.43,1780.125C591.688,1792.958,700.203,1801.292,754.461,1808.958C808.719,1816.625,808.719,1823.625,808.719,1827.125L808.719,1830.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C11_K_41" d="M331.977,1759.625L339.109,1763.792C346.24,1767.958,360.503,1776.292,407.158,1784.625C453.813,1792.958,532.859,1801.292,572.383,1808.958C611.906,1816.625,611.906,1823.625,611.906,1827.125L611.906,1830.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D5_L_42" d="M1958.376,1129.75L1967.431,1133.917C1976.485,1138.083,1994.594,1146.417,2003.649,1165.073C2012.703,1183.729,2012.703,1212.708,2012.703,1243.688C2012.703,1274.667,2012.703,1307.646,2012.703,1342.802C2012.703,1377.958,2012.703,1415.292,2012.703,1452.625C2012.703,1489.958,2012.703,1527.292,2012.703,1556.625C2012.703,1585.958,2012.703,1607.292,2012.703,1626.625C2012.703,1645.958,2012.703,1663.292,2012.703,1680.625C2012.703,1697.958,2012.703,1715.292,2012.703,1732.625C2012.703,1749.958,2012.703,1767.292,1940.555,1780.125C1868.406,1792.958,1724.109,1801.292,1651.961,1808.958C1579.813,1816.625,1579.813,1823.625,1579.813,1827.125L1579.813,1830.625"></path><path marker-end="url(#mermaid-45b970ab-e1c0-4821-a51f-7406bfa86072_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D9_M_43" d="M1670.04,1655.625L1675.416,1659.792C1680.792,1663.958,1691.545,1672.292,1696.921,1685.125C1702.297,1697.958,1702.297,1715.292,1702.297,1732.625C1702.297,1749.958,1702.297,1767.292,1637.898,1780.125C1573.5,1792.958,1444.703,1801.292,1380.305,1808.958C1315.906,1816.625,1315.906,1823.625,1315.906,1827.125L1315.906,1830.625"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(849.484375, 299)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>图片上传</p></span></div></foreignObject></g></g><g transform="translate(2324.21875, 299)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>视频生成</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(594.171875, 824.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1110.53125, 1102.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(186.171875, 1340.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(384.171875, 1340.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1899.703125, 1038.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(2313.40625, 1038.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1635.203125, 1564.625)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>适合</p></span></div></foreignObject></g></g><g transform="translate(1833.203125, 1564.625)" class="edgeLabel"><g transform="translate(-24, -12)" class="label"><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>不适合</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1475.1875, 35)" id="flowchart-A-1169" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户请求</p></span></div></foreignObject></g></g><g transform="translate(1475.1875, 187)" id="flowchart-B-1170" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"></polygon><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>请求类型判断</p></span></div></foreignObject></g></g><g transform="translate(849.484375, 363)" id="flowchart-C-1172" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>图片标注流程</p></span></div></foreignObject></g></g><g transform="translate(2324.21875, 363)" id="flowchart-D-1174" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>视频生成流程</p></span></div></foreignObject></g></g><g transform="translate(849.484375, 467)" id="flowchart-C1-1176" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>图片预处理</p></span></div></foreignObject></g></g><g transform="translate(849.484375, 571)" id="flowchart-C2-1178" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>人脸检测</p></span></div></foreignObject></g></g><g transform="translate(650.671875, 717.9375)" id="flowchart-C3-1180" class="node default"><polygon transform="translate(-69.9375,69.9375)" class="label-container" points="69.9375,0 139.875,-69.9375 69.9375,-139.875 0,-69.9375"></polygon><g transform="translate(-42.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="85.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检测到人脸?</p></span></div></foreignObject></g></g><g transform="translate(594.171875, 931.8125)" id="flowchart-C4-1182" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>人脸特征提取</p></span></div></foreignObject></g></g><g transform="translate(1110.53125, 1452.625)" id="flowchart-C5-1184" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>场景分析</p></span></div></foreignObject></g></g><g transform="translate(388.203125, 1102.75)" id="flowchart-C6-1186" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>角色匹配</p></span></div></foreignObject></g></g><g transform="translate(285.171875, 1241.6875)" id="flowchart-C7-1188" class="node default"><polygon transform="translate(-61.9375,61.9375)" class="label-container" points="61.9375,0 123.875,-61.9375 61.9375,-123.875 0,-61.9375"></polygon><g transform="translate(-34.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="69.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>匹配成功?</p></span></div></foreignObject></g></g><g transform="translate(186.171875, 1452.625)" id="flowchart-C8-1190" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>生成角色标注</p></span></div></foreignObject></g></g><g transform="translate(384.171875, 1452.625)" id="flowchart-C9-1192" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>标记为未知</p></span></div></foreignObject></g></g><g transform="translate(384.171875, 1628.625)" id="flowchart-C10-1194" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>地理位置识别</p></span></div></foreignObject></g></g><g transform="translate(285.765625, 1732.625)" id="flowchart-C11-1200" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>场景描述生成</p></span></div></foreignObject></g></g><g transform="translate(86, 1861.625)" id="flowchart-C12-1202" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>关系图谱构建</p></span></div></foreignObject></g></g><g transform="translate(86, 2015.625)" id="flowchart-C13-1204" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>返回标注结果</p></span></div></foreignObject></g></g><g transform="translate(2324.21875, 467)" id="flowchart-D1-1206" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>事件数据处理</p></span></div></foreignObject></g></g><g transform="translate(2324.21875, 571)" id="flowchart-D2-1208" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>主角信息获取</p></span></div></foreignObject></g></g><g transform="translate(2324.21875, 717.9375)" id="flowchart-D3-1210" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>视频脚本生成</p></span></div></foreignObject></g></g><g transform="translate(2068.015625, 931.8125)" id="flowchart-D4-1212" class="node default"><polygon transform="translate(-69.9375,69.9375)" class="label-container" points="69.9375,0 139.875,-69.9375 69.9375,-139.875 0,-69.9375"></polygon><g transform="translate(-42.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="85.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>使用数字人?</p></span></div></foreignObject></g></g><g transform="translate(1899.703125, 1102.75)" id="flowchart-D5-1214" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数字人开场生成</p></span></div></foreignObject></g></g><g transform="translate(2313.40625, 1102.75)" id="flowchart-D6-1216" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>直接进入主体</p></span></div></foreignObject></g></g><g transform="translate(1790.703125, 1241.6875)" id="flowchart-D7-1218" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>主体内容处理</p></span></div></foreignObject></g></g><g transform="translate(1790.703125, 1452.625)" id="flowchart-D8-1222" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"></polygon><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>智能动画判断</p></span></div></foreignObject></g></g><g transform="translate(1635.203125, 1628.625)" id="flowchart-D9-1224" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>图片转视频</p></span></div></foreignObject></g></g><g transform="translate(1833.203125, 1628.625)" id="flowchart-D10-1226" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>静态图片处理</p></span></div></foreignObject></g></g><g transform="translate(1586.703125, 1732.625)" id="flowchart-D11-1228" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>音频合成</p></span></div></foreignObject></g></g><g transform="translate(1965.90625, 1861.625)" id="flowchart-D12-1232" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>视频片段合成</p></span></div></foreignObject></g></g><g transform="translate(2338.21875, 2015.625)" id="flowchart-D13-1234" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>最终视频输出</p></span></div></foreignObject></g></g><g transform="translate(1507.40625, 2015.625)" id="flowchart-E-1235" class="node default"><rect height="54" width="140" y="-27" x="-70" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>多模态理解</p></span></div></foreignObject></g></g><g transform="translate(384.5, 2015.625)" id="flowchart-F-1236" class="node default"><rect height="54" width="140" y="-27" x="-70" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>上下文分析</p></span></div></foreignObject></g></g><g transform="translate(2112.21875, 2015.625)" id="flowchart-G-1237" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户偏好学习</p></span></div></foreignObject></g></g><g transform="translate(1922.21875, 2015.625)" id="flowchart-H-1238" class="node default"><rect height="54" width="124" y="-27" x="-62" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>质量评估</p></span></div></foreignObject></g></g><g transform="translate(1005.53125, 1861.625)" id="flowchart-I-1251" class="node default"><rect height="54" width="146.8125" y="-27" x="-73.40625" style="" class="basic label-container"></rect><g transform="translate(-43.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>人脸识别API</p></span></div></foreignObject></g></g><g transform="translate(808.71875, 1861.625)" id="flowchart-J-1252" class="node default"><rect height="54" width="146.8125" y="-27" x="-73.40625" style="" class="basic label-container"></rect><g transform="translate(-43.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>地理位置API</p></span></div></foreignObject></g></g><g transform="translate(611.90625, 1861.625)" id="flowchart-K-1253" class="node default"><rect height="54" width="146.8125" y="-27" x="-73.40625" style="" class="basic label-container"></rect><g transform="translate(-43.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>文本生成API</p></span></div></foreignObject></g></g><g transform="translate(1579.8125, 1861.625)" id="flowchart-L-1254" class="node default"><rect height="54" width="146.8125" y="-27" x="-73.40625" style="" class="basic label-container"></rect><g transform="translate(-43.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>图像生成API</p></span></div></foreignObject></g></g><g transform="translate(1315.90625, 1861.625)" id="flowchart-M-1255" class="node default"><rect height="54" width="146.8125" y="-27" x="-73.40625" style="" class="basic label-container"></rect><g transform="translate(-43.40625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>视频生成API</p></span></div></foreignObject></g></g></g></g></g></svg>