<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="27.1.6">
  <diagram name="Flowchart-2" id="Flowchart-2-id">
    <mxGraphModel dx="2270" dy="829" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="A" target="B">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="A" value="用户请求" style="shape=rectangle;rounded=1;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="375" y="21" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="B" target="c2b4JkFLpF9g5OxbSaXH-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="B" target="c2b4JkFLpF9g5OxbSaXH-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="B" value="请求类型判断" style="shape=rhombus;rounded=0;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="375" y="108" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C" target="C1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C" value="图片标注流程" style="shape=rectangle;rounded=1;html=1;fillColor=#e1f5fe;" parent="1" vertex="1">
          <mxGeometry x="156" y="168" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D" target="D1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D" value="视频生成流程" style="shape=rectangle;rounded=1;html=1;fillColor=#f3e5f5;" parent="1" vertex="1">
          <mxGeometry x="574" y="168" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C1" target="C2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C1" value="图片预处理" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="166" y="243" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C2" target="C3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C2" value="人脸检测" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="166" y="320" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C3" target="C4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-22" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-10">
          <mxGeometry x="-0.5652" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C3" target="C5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-23" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-20">
          <mxGeometry x="0.0811" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C3" value="检测到人脸?" style="shape=rhombus;rounded=0;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="66" y="373" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="C4" target="C6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="C4" target="I">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C4" value="人脸特征提取" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="56" y="456" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C5" target="C10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C5" value="场景分析" style="shape=rectangle;rounded=1;html=1;fillColor=light-dark(#e1f5fe, #ededed);" parent="1" vertex="1">
          <mxGeometry x="194" y="621" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6" target="C7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C6" target="F">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C6" value="角色匹配" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="-44" y="553" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="C7" target="C8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-24" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-13">
          <mxGeometry x="-0.2491" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="C7" target="C9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-25" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-14">
          <mxGeometry x="-0.5148" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="C7" value="匹配成功?" style="shape=rhombus;rounded=0;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="-44" y="651" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C8" target="C10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C8" value="生成角色标注" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="-170" y="729" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C9" target="C10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C9" value="标记为未知" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="66" y="729" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C10" target="C11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="C10" target="J">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C10" value="地理位置识别" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="-54" y="853" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C11" target="C12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C11" target="K">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C11" value="场景描述生成" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="-54" y="957" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="C12" target="C13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="C12" target="E">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="C12" value="关系图谱构建" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="-54" y="1091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C13" value="返回标注结果" style="shape=rectangle;rounded=1;html=1;strokeColor=default;fillColor=#E1F5FE;" parent="1" vertex="1">
          <mxGeometry x="-64" y="1184" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D1" target="D2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D1" value="事件数据处理" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="574" y="243" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="D2" target="D3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D2" value="主角信息获取" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="574" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D3" target="D4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="D3" target="K">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="D3" target="G">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D3" value="视频脚本生成" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="574" y="406" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D4" target="D5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-33" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-31">
          <mxGeometry x="-0.1315" y="4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="D4" target="D6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-34" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-32">
          <mxGeometry x="-0.2911" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="D4" value="使用数字人?" style="shape=rhombus;rounded=0;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="574" y="487" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="D5" target="D7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="D5" target="L">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D5" value="数字人开场生成" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="434" y="558" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="D6" target="D7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D6" value="直接进入主体" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="694" y="558" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D7" target="D8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D7" value="主体内容处理" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="574" y="658" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D8" target="D9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-40" value="是" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-38">
          <mxGeometry x="-0.3686" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D8" target="D10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-41" value="否" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c2b4JkFLpF9g5OxbSaXH-39">
          <mxGeometry x="-0.5333" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="D8" value="智能动画判断" style="shape=rhombus;rounded=0;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="574" y="744" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="D9" target="D11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="D9" target="M">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D9" value="图片转视频" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="454" y="816" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-43" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="D10" target="D11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D10" value="静态图片处理" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="694" y="816" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D11" target="D12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D11" value="音频合成" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="574" y="903" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D12" target="D13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D12" value="视频片段合成" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="564" y="993" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="D13" target="E">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="D13" target="H">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="D13" value="最终视频输出" style="shape=rectangle;rounded=1;html=1;fillColor=#F3E5F5;" parent="1" vertex="1">
          <mxGeometry x="564" y="1091" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="E" value="多模态理解" style="shape=rectangle;rounded=1;html=1;fillColor=#fff3e0;" parent="1" vertex="1">
          <mxGeometry x="255" y="1091" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="F" value="上下文分析" style="shape=rectangle;rounded=1;html=1;fillColor=#fff3e0;" parent="1" vertex="1">
          <mxGeometry x="-206" y="553" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="G" value="用户偏好学习" style="shape=rectangle;rounded=1;html=1;fillColor=#fff3e0;" parent="1" vertex="1">
          <mxGeometry x="780" y="406" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="H" value="质量评估" style="shape=rectangle;rounded=1;html=1;fillColor=#fff3e0;" parent="1" vertex="1">
          <mxGeometry x="574" y="1189" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="I" value="人脸识别API" style="shape=rectangle;rounded=1;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="-206" y="456" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="J" value="地理位置API" style="shape=rectangle;rounded=1;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="-290" y="853" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="K" value="文本生成API" style="shape=rectangle;rounded=1;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="310" y="729" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="L" value="图像生成API" style="shape=rectangle;rounded=1;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="375" y="452" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="M" value="视频生成API" style="shape=rectangle;rounded=1;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="310" y="816" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="c2b4JkFLpF9g5OxbSaXH-2" target="C">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-2" value="图片上传" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E1F5FE;strokeColor=default;" vertex="1" parent="1">
          <mxGeometry x="176" y="121" width="80" height="34" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="c2b4JkFLpF9g5OxbSaXH-3" target="D">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2b4JkFLpF9g5OxbSaXH-3" value="视频生成" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#F3E5F5;" vertex="1" parent="1">
          <mxGeometry x="594" y="121" width="80" height="34" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
