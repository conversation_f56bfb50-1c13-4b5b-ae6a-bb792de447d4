<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="27.1.6">
  <diagram name="Page-1" id="Page-1-id">
    <mxGraphModel dx="1844" dy="580" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="A" value="用户上传图片" style="shape=rectangle;rounded=1;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="-250" y="304" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="B" value="用户选择视频生成" style="shape=rectangle;rounded=1;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="-90" y="304" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="C" value="用户查看结果" style="shape=rectangle;rounded=1;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="-22" y="839" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="D" value="请求路由器" style="shape=rectangle;rounded=1;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="-170" y="404" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0;entryDx=0;entryDy=0;" parent="1" source="E" target="G" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="1" source="E" target="L" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E" value="任务调度器" style="shape=rectangle;rounded=1;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="-22" y="404" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="F" target="X" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="F" value="状态管理器" style="shape=rectangle;rounded=1;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="140.5" y="404" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="G" value="人脸检测模块" style="shape=rectangle;rounded=1;fillColor=#e1f5fe;html=1;" parent="1" vertex="1">
          <mxGeometry x="-175" y="486" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="H" target="V" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="H" value="角色识别模块" style="shape=rectangle;rounded=1;fillColor=#e1f5fe;html=1;" parent="1" vertex="1">
          <mxGeometry x="-175" y="577" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="AnzpFAS0cgPWIXRr9etc-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="I" target="R">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="I" value="地理位置识别" style="shape=rectangle;rounded=1;fillColor=#e1f5fe;html=1;" parent="1" vertex="1">
          <mxGeometry x="-175" y="664" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="J" target="R" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="J" value="场景描述生成" style="shape=rectangle;rounded=1;fillColor=#e1f5fe;html=1;" parent="1" vertex="1">
          <mxGeometry x="-175" y="745" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="K" target="C" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="K" value="关系图谱构建" style="shape=rectangle;rounded=1;fillColor=#e1f5fe;html=1;" parent="1" vertex="1">
          <mxGeometry x="-175" y="839" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="N" target="R" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="105" y="644" />
              <mxPoint x="38" y="644" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="N" target="P" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="N" value="数字人生成" style="shape=rectangle;rounded=1;fillColor=#f3e5f5;html=1;" parent="1" vertex="1">
          <mxGeometry x="45" y="664" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="O" target="R" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="275" y="644" />
              <mxPoint x="38" y="644" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="O" target="P" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="O" value="图片转视频" style="shape=rectangle;rounded=1;fillColor=#f3e5f5;html=1;" parent="1" vertex="1">
          <mxGeometry x="215" y="664" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P" value="音频合成" style="shape=rectangle;rounded=1;fillColor=#f3e5f5;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="745" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="Q" target="C" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Q" value="视频合成" style="shape=rectangle;rounded=1;fillColor=#f3e5f5;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="839" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="R" value="外部api" style="shape=rectangle;rounded=1;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="-32" y="486" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="U" value="用户数据库" style="shape=ellipse;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="-164.5" y="933" width="99" height="99" as="geometry" />
        </mxCell>
        <mxCell id="V" value="角色数据库" style="shape=ellipse;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="-321" y="557.5" width="99" height="99" as="geometry" />
        </mxCell>
        <mxCell id="W" value="媒体文件存储" style="shape=ellipse;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="140.5" y="933" width="99" height="99" as="geometry" />
        </mxCell>
        <mxCell id="X" value="任务状态存储" style="shape=ellipse;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="326" y="384.5" width="99" height="99" as="geometry" />
        </mxCell>
        <mxCell id="E1" style="edgeStyle=orthogonalEdgeStyle;html=1;rounded=0;" parent="1" source="A" target="D" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E2" style="edgeStyle=orthogonalEdgeStyle;html=1;rounded=0;" parent="1" source="B" target="D" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E3" parent="1" source="D" target="E" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E4" parent="1" source="E" target="F" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E6" parent="1" source="G" target="H" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E7" parent="1" source="H" target="I" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E8" parent="1" source="I" target="J" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E9" parent="1" source="J" target="K" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E11" parent="1" source="L" target="M" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E16" parent="1" source="P" target="Q" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E17" parent="1" source="G" target="R" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E20" parent="1" source="L" target="R" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E24" parent="1" source="K" target="U" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E25" parent="1" source="Q" target="W" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="L" value="脚本生成模块" style="shape=rectangle;rounded=1;fillColor=#f3e5f5;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="486" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="M" target="N" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="M" target="O" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="M" value="智能动画判断" style="shape=rectangle;rounded=1;fillColor=#f3e5f5;html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="577" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-8" value="用户交互" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="509" y="401" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-9" value="智能体协调" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="509" y="487" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-10" value="图片标注" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="509" y="574" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-11" value="视频生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="509" y="667" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-12" value="数据存储" style="shape=rectangle;rounded=1;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="509" y="760" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Wm1AieO-7C3ApRHEibw2-30" value="外部服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="509" y="853" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
